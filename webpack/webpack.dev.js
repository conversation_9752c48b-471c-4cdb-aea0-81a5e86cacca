const webpack = require("webpack");
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');

module.exports = (env = {}) => {
  return require('./webpack.base')({
    mode: "development",
    devServer: {
      hot: true,
      open: true,
      historyApiFallback: true,
      port: 8080,
      compress: true,
      proxy: {
        "/api": {
          target: "https://operator-dev.support24.uz",
          secure: false,
          changeOrigin: true,
        }
      }
    },
    plugins: [
      new webpack.SourceMapDevToolPlugin({
        filename: "[file].map",
      }),
      new webpack.HotModuleReplacementPlugin(),
      new ReactRefreshWebpackPlugin(),
    ],
    devtool: "eval-cheap-module-source-map",
  }, env);
};