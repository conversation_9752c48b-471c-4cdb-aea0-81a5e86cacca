import i18next, { i18n as i18nInstance } from "i18next";
import detector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";

import { ELanguages, namespaces } from "./i18n.constants";
import ruAuth from "./locales/ru/auth.json";
import ruButtons from "./locales/ru/buttons.json";
import ruContractPricing from "./locales/ru/contractPricing.json";
import ruCustomers from "./locales/ru/customers.json";
import ruFields from "./locales/ru/fields.json";
import ruNotifications from "./locales/ru/notifications.json";
import ruRepairTickets from "./locales/ru/repairTickets.json";
import uzAuth from "./locales/uz/auth.json";
import uzButtons from "./locales/uz/buttons.json";
import uzContractPricing from "./locales/uz/contractPricing.json";
import uzCustomers from "./locales/uz/customers.json";
import uzFields from "./locales/uz/fields.json";
import uzNotifications from "./locales/uz/notifications.json";
import uzRepairTickets from "./locales/uz/repairTickets.json";

export const defaultNS = namespaces.fields;

type TResources = {
  ru: {
    [namespaces.fields]: typeof ruFields;
    [namespaces.buttons]: typeof ruButtons;
    [namespaces.notifications]: typeof ruNotifications;
    [namespaces.customers]: typeof ruCustomers;
    [namespaces.auth]: typeof ruAuth;
    [namespaces.contractPricing]: typeof ruContractPricing;
    [namespaces.repairTickets]: typeof ruRepairTickets;
  };
  uz: {
    [namespaces.fields]: typeof uzFields;
    [namespaces.buttons]: typeof uzButtons;
    [namespaces.notifications]: typeof uzNotifications;
    [namespaces.customers]: typeof uzCustomers;
    [namespaces.auth]: typeof uzAuth;
    [namespaces.contractPricing]: typeof uzContractPricing;
    [namespaces.repairTickets]: typeof uzRepairTickets;
  };
};

export const resources: TResources = {
  ru: {
    [namespaces.fields]: ruFields,
    [namespaces.buttons]: ruButtons,
    [namespaces.notifications]: ruNotifications,
    [namespaces.customers]: ruCustomers,
    [namespaces.auth]: ruAuth,
    [namespaces.contractPricing]: ruContractPricing,
  },
  uz: {
    [namespaces.fields]: uzFields,
    [namespaces.buttons]: uzButtons,
    [namespaces.notifications]: uzNotifications,
    [namespaces.customers]: uzCustomers,
    [namespaces.auth]: uzAuth,
    [namespaces.contractPricing]: uzContractPricing,
  },
};

const createI18n = (language: string): i18nInstance => {
  const i18n = i18next.createInstance().use(initReactI18next);

  i18n.use(detector).init({
    lng: language,
    fallbackLng: language,
    ns: namespaces.fields,
    defaultNS,
    resources,
  });

  return i18n;
};

export const i18n = createI18n(localStorage.getItem("i18nextLng") || ELanguages.RU);
