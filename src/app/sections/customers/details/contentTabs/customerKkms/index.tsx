import React, { useEffect, useMemo, useState } from "react";

import { FileDoneOutlined, ReloadOutlined } from "@ant-design/icons";
import { Al<PERSON>, Popover } from "antd";
import { ColumnsType } from "antd/lib/table/interface";
import { useStore } from "effector-react";
import { useTranslation } from "react-i18next";

import { TCodeNameModel } from "@businessLogic/models/common";

import { $checkTransaction } from "@stores/branchCustomer";
import { $customerDetails } from "@stores/customer";
import {
  $customerBranchDevicesSyncSmartpos,
  $customerChangeTariffDevices,
  $downloadKKMApplicationFile,
} from "@stores/customerBranchDevice";
import { $clientKKMsList, $refreshCustomerDeviceStatus } from "@stores/customerDevice";
import { $uploadEntityDocument } from "@stores/fileUpload";
import { $confirmCustomer } from "@stores/ofd";

import { i18n } from "@localization/i18n";
import { namespaces } from "@localization/i18n.constants";

import { ButtonCustom } from "@ui/buttonCustom";
import { ContentTabs } from "@ui/contentTabs";
import { ModalCustom } from "@ui/modalCustom";
import { TableCustom } from "@ui/tableCustom";
import { UploadFileModal } from "@ui/uploadFileModal";

import { getFormattedDate, openNotificationWithIcon } from "@utils/helpers";

import { useModalControl } from "@hooks/useModalControl";
import { useQueryParams } from "@hooks/useQueryParams";

import { DOCUMENT_TYPES, DOT_DATE_FORMAT_WITH_TIME, ENTITY_TYPES, NO_DATA } from "@constants/index";

import apay from "@assets/images/apay.png";
import click from "@assets/images/click.png";
import humo from "@assets/images/humo-logo.svg";
// import humo from "@assets/images/humo.png";
import payme from "@assets/images/payme.png";
import uzcard from "@assets/images/uzcard.png";
import { MenuIcon } from "@assets/svg";

import { $customerKkmsFilterProps } from "../../../models";

import { ClientKKMsFilter } from "./clientKkmsFilter";
import {
  CreateDeviceModelCombinationsModal,
  TCreateDeviceModelCombinationsModalControl,
} from "./createDeviceModelCombinationsModal";
import { GenerateTemplateModal } from "./generateTemplate";
import { KkmTemplateModal } from "./kkmTemplateModal";
import { RegisterCustomerDeviceModal } from "./registerCustomerDeviceModal";
import { useStyles } from "./styles";

const columns: ColumnsType<any> = [
  {
    title: "№",
    dataIndex: "number",
    width: 50,
  },
  {
    title: i18n.t("kkm"),
    dataIndex: "deviceSerial",
    align: "center",
  },
  {
    title: i18n.t("fm"),
    dataIndex: "fiscalModuleSerial",
  },
  {
    title: i18n.t("deviceType"),
    dataIndex: "deviceType",
  },
  {
    title: i18n.t("tt"),
    dataIndex: "customerBranch",
  },
  {
    title: i18n.t("documents"),
    dataIndex: "ofdFileUrl",
  },
  {
    title: i18n.t("paymentType"),
    dataIndex: "paymentType",
  },
  {
    title: i18n.t("status"),
    dataIndex: "deviceStatus",
  },
  {
    title: i18n.t("receipt"),
    dataIndex: "fiscalCheckUrl",
    align: "center",
  },
  {
    title: i18n.t("fiscalizationDate"),
    dataIndex: "fiscalizationDate",
  },
  {
    title: i18n.t("refresh", { ns: namespaces.buttons }),
    dataIndex: "refresh",
    align: "center",
  },
  {
    title: "",
    dataIndex: "actionMenu",
    width: 50,
  },
];

const getImgType = (v: any) => {
  if (v?.code.includes("UZ_CARD")) return uzcard;
  else if (v?.code.includes("HUMO")) return humo;
  else if (v?.code === "CLICK") return click;
  else if (v?.code === "PAY_ME") return payme;
  // if (v?.code === 'A_PAY')
  else return apay;
};

export const CustomerKkmsList = () => {
  const { t } = useTranslation();

  const classes = useStyles();

  const uploadFileModalControl = useModalControl();
  const generateTemplateModalControl = useModalControl<any>();
  const kkmTemplateModalControl = useModalControl();
  const registerCustomerDeviceModalControl = useModalControl<any>();
  const createDeviceModelCombinationsModalControl =
    useModalControl<TCreateDeviceModelCombinationsModalControl>();

  const confirmCustomerState = useStore($confirmCustomer.store);
  const customerKKMsState = useStore($clientKKMsList.store);
  const customerDetailsState = useStore($customerDetails.store);
  const customerChangeTariffDevicesState = useStore($customerChangeTariffDevices.store);
  const downloadKKMApplicationFileState = useStore($downloadKKMApplicationFile.store);
  const customerBranchDevicesSyncSmartposState = useStore($customerBranchDevicesSyncSmartpos.store);
  const checkTransactionState = useStore($checkTransaction.store);
  const refreshCustomerDeviceStatusState = useStore($refreshCustomerDeviceStatus.store);

  const { queryParams, additionalParams, onFilterChange, clearFilter } =
    useQueryParams($customerKkmsFilterProps);

  const [actionMenuVisible, setActionMenuVisible] = useState(null);

  const { data: clientKKMsData, error, loading } = customerKKMsState;
  const {
    content: kkms,
    number: kkmsPageNumber,
    size: kkmsPageSize,
    totalElements: kkmsTotal,
  } = clientKKMsData;

  const { id: customerId, tin } = customerDetailsState.data!;

  useEffect(() => {
    if (checkTransactionState.data?.billStatus?.name && !checkTransactionState.loading) {
      openNotificationWithIcon("info", checkTransactionState.data?.billStatus?.name);
      $checkTransaction.reset();
    }
  }, [checkTransactionState.loading]);

  useEffect(() => {
    getKkms();
  }, [queryParams]);

  useEffect(() => {
    getCustomerChangeTariffDevices();

    return () => {
      $clientKKMsList.reset();
    };
  }, []);

  useEffect(() => {
    if (customerBranchDevicesSyncSmartposState.success) {
      openNotificationWithIcon("success", t("successRegister", { ns: namespaces.customers }));
      $customerBranchDevicesSyncSmartpos.reset();
    }
  }, [customerBranchDevicesSyncSmartposState.success]);

  const getKkms = () => {
    $clientKKMsList.effect({
      ...queryParams,
      customerId,
    });
  };

  const getCustomerChangeTariffDevices = () => $customerChangeTariffDevices.effect({ id: customerId });

  const onPaginationChange = (page: number, size: number) => {
    onFilterChange({ page: page - 1, size });
  };

  useEffect(() => {
    if (confirmCustomerState.success) {
      openNotificationWithIcon("success", t("confirmed", { ns: namespaces.customers }));
      $confirmCustomer.reset();
    }
  }, [confirmCustomerState.success]);

  useEffect(() => {
    if (refreshCustomerDeviceStatusState.success) {
      openNotificationWithIcon("success", t("success", { ns: namespaces.notifications }));
      $refreshCustomerDeviceStatus.reset();
      getKkms();
    }
  }, [refreshCustomerDeviceStatusState.success]);

  const dataSource = useMemo(() => {
    return kkms.map((kkm, index) => {
      return {
        id: kkm.id,
        key: kkm.id,
        number: <div className="w-s-n">{kkmsPageSize * kkmsPageNumber + index + 1}</div>,
        paymentStatuses: kkm.paymentStatuses,
        deviceSerial: (
          <div>
            <span>{kkm.deviceSerial}</span>
            {/*<div*/}
            {/*  style={{*/}
            {/*    display: "flex",*/}
            {/*    alignItems: "center",*/}
            {/*    justifyContent: "space-between",*/}
            {/*    flexWrap: "wrap",*/}
            {/*    width: 100,*/}
            {/*  }}*/}
            {/*>*/}
            {/*  {!lodash.isEmpty(kkm.paymentStatuses) &&*/}
            {/*    kkm.paymentStatuses.map(*/}
            {/*      (v) =>*/}
            {/*        v?.code !== "NOT_CONNECTED" && (*/}
            {/*          <img*/}
            {/*            style={{ width: 49, height: 25, objectFit: "contain", marginBottom: 4 }}*/}
            {/*            src={getImgType(v)}*/}
            {/*          />*/}
            {/*        ),*/}
            {/*    )}*/}
            {/*</div>*/}
          </div>
        ),
        fiscalModuleSerial: kkm.fiscalModuleSerial,
        deviceType: kkm.deviceType ? kkm.deviceType.name : "-",
        customerBranch: kkm.customerBranch ? kkm.customerBranch.name : NO_DATA,
        ofdFileUrl: (
          <div>
            {kkm.ofdFileUrl ? (
              <div className="application-file-wrap">
                <a href={kkm.ofdFileUrl} target="_blank" rel="noreferrer">
                  {t("ofdDoc")}
                </a>
              </div>
            ) : (
              t("no")
            )}
            {kkm.applicationFileUrl ? (
              <div className="application-file-wrap">
                <a href={kkm.applicationFileUrl} target="_blank" download rel="noreferrer">
                  {t("applicationAr")}
                </a>
              </div>
            ) : (
              ""
            )}
          </div>
        ),
        paymentType: kkm.paymentType?.name || NO_DATA,
        deviceStatus: kkm.deviceStatus?.name,
        fiscalCheckUrl: kkm.bill?.fiscalCheckUrl ? (
          <a href={kkm.bill.fiscalCheckUrl} target="_blank" rel="noreferrer">
            <FileDoneOutlined style={{ fontSize: "1.5rem" }} />
          </a>
        ) : (
          "-"
        ),
        fiscalizationDate: getFormattedDate(kkm.fiscalizationDate, DOT_DATE_FORMAT_WITH_TIME),
        refresh: (
          <ButtonCustom
            type="default"
            loading={
              refreshCustomerDeviceStatusState.deviceId === kkm.deviceId &&
              refreshCustomerDeviceStatusState.loading
            }
            onClick={() => $refreshCustomerDeviceStatus.effect(kkm.deviceId)}
            icon={<ReloadOutlined />}
            disabled={refreshCustomerDeviceStatusState.loading}
          />
        ),
        actionMenu: (
          <Popover
            overlayClassName="custom__popover"
            placement="left"
            open={actionMenuVisible === kkm.id}
            onOpenChange={(visible) => setActionMenuVisible(visible ? kkm.id : null)}
            trigger="click"
            content={
              <div>
                <div className="custom__popover__item">
                  <ButtonCustom
                    type="default"
                    disabled={kkm?.paymentType?.code !== "A_PAY"}
                    onClick={() => $checkTransaction.effect(kkm.bill?.billId)}
                  >
                    {t("checkPaymentStatus", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </div>
                <div className="custom__popover__item">
                  <ButtonCustom
                    type="default"
                    disabled={kkm?.paymentType?.code !== "A_PAY"}
                    onClick={() =>
                      registerCustomerDeviceModalControl.openModal({
                        billIdProp: kkm.bill?.billId,
                        customerId,
                        tin,
                        isShowQrCode: true,
                      })
                    }
                  >
                    {t("paymentMenu", { ns: namespaces.customers })}
                  </ButtonCustom>
                </div>
                <div className="custom__popover__item">
                  <ButtonCustom
                    type="default"
                    disabled={!kkm.fiscalizationId}
                    onClick={() =>
                      generateTemplateModalControl.openModal({
                        customerId,
                        fiscalizationId: kkm.fiscalizationId,
                      })
                    }
                  >
                    {t("template")}
                  </ButtonCustom>
                </div>
                <div className="custom__popover__item">
                  <ButtonCustom
                    type="default"
                    loading={downloadKKMApplicationFileState.loading}
                    onClick={() =>
                      $downloadKKMApplicationFile.effect({
                        customerBranchId: kkm.customerBranch?.id,
                        deviceSerial: kkm.deviceSerial,
                        fiscalModuleSerial: kkm.fiscalModuleSerial,
                      })
                    }
                  >
                    {t("downloadApplication", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </div>
                <div className="custom__popover__item">
                  <ButtonCustom
                    type="default"
                    onClick={() =>
                      uploadFileModalControl.openModal({
                        params: {
                          entityId: kkm.fiscalizationId,
                          entityType: ENTITY_TYPES.FISCALIZATION,
                          documentType: DOCUMENT_TYPES.APPLICATION,
                        },
                      })
                    }
                  >
                    {t("uploadApplication", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </div>
                <div className="custom__popover__item">
                  <ButtonCustom
                    type="default"
                    disabled={!kkm.sync}
                    loading={confirmCustomerState.loading}
                    onClick={() => {
                      if (kkm.customerBranch && kkm.customerBranch.id) {
                        const data = {
                          customerId,
                          customerBranchId: kkm.customerBranch.id,
                          deviceSerial: kkm.deviceSerial,
                          fiscalModuleSerial: kkm.fiscalModuleSerial,
                        };

                        $confirmCustomer.effect(data);
                      }
                    }}
                  >
                    {t("confirm", { ns: namespaces.customers })}
                  </ButtonCustom>
                </div>
                <div className="custom__popover__item">
                  <ButtonCustom
                    type="default"
                    loading={customerBranchDevicesSyncSmartposState.loading}
                    onClick={() => $customerBranchDevicesSyncSmartpos.effect(kkm.customerBranchDeviceId)}
                  >
                    {t("registerSmartpos", { ns: namespaces.buttons })}
                  </ButtonCustom>
                </div>
              </div>
            }
          >
            <ButtonCustom type="default" className="custom__popover-btn-2">
              <MenuIcon />
            </ButtonCustom>
          </Popover>
        ),
      };
    });
  }, [
    kkms,
    actionMenuVisible,
    confirmCustomerState.loading,
    downloadKKMApplicationFileState.loading,
    customerBranchDevicesSyncSmartposState.loading,
    refreshCustomerDeviceStatusState.loading,
    refreshCustomerDeviceStatusState.deviceId,
  ]);

  useEffect(() => {
    if (downloadKKMApplicationFileState.error) {
      const asyncF = async () => {
        // @ts-ignore
        const response = await downloadKKMApplicationFileState.error.text(); // TODO check it
        openNotificationWithIcon("error", JSON.parse(response)?.detail);
      };
      asyncF();
    }
  }, [downloadKKMApplicationFileState.error]);

  const expandedRowKeys = useMemo(() => {
    return dataSource
      .filter((data) => {
        if (!data.paymentStatuses) {
          return false;
        }
        if (data.paymentStatuses.length === 1) {
          return data.paymentStatuses[0].code !== "NOT_CONNECTED";
        }

        return true;
      })
      .map((data) => data.key);
  }, [dataSource]);

  const getPaymentProviderImage = (paymentStatuses: TCodeNameModel[]) => {
    const result = paymentStatuses.filter((v) => v?.code !== "NOT_CONNECTED");

    if (!result.length) {
      return "-";
    }

    return result.map((v) => (
      <img
        style={{ width: 49, height: 25, objectFit: "contain", marginBottom: 4 }}
        src={getImgType(v)}
        alt="payment-type"
      />
    ));
  };

  return (
    <>
      <ContentTabs>
        <ClientKKMsFilter
          queryParams={queryParams}
          additionalParams={additionalParams}
          clearQueryParams={clearFilter}
          onFilterChange={onFilterChange}
        />

        <ContentTabs.Actions>
          <div
            style={{
              display: "flex",
            }}
          >
            <ButtonCustom
              type="primary"
              size="middle"
              onClick={() => registerCustomerDeviceModalControl.openModal({ customerId, tin })}
            >
              {t("register", { ns: namespaces.buttons })}
            </ButtonCustom>
            <ButtonCustom
              type="primary"
              size="middle"
              onClick={() => createDeviceModelCombinationsModalControl.openModal({ customerId })}
            >
              {t("linkDevices", { ns: namespaces.buttons })}
            </ButtonCustom>
          </div>
        </ContentTabs.Actions>
      </ContentTabs>

      {error && (
        <div className="m-b-20">
          <Alert message={error.detail || error.title} type="error" />
        </div>
      )}

      <TableCustom
        loading={loading || customerChangeTariffDevicesState.loading}
        dataSource={dataSource}
        columns={columns}
        pagination={{
          total: kkmsTotal,
          pageSize: kkmsPageSize,
          current: queryParams.page ? queryParams.page + 1 : 1,
          onChange: onPaginationChange,
        }}
        expandable={{
          expandIcon: () => null,
          expandedRowRender: (row: (typeof dataSource)[number]) => {
            return <div className={classes.expandedRow}>{getPaymentProviderImage(row.paymentStatuses)}</div>;
          },
          expandedRowKeys: expandedRowKeys,
        }}
      />

      <ModalCustom open={uploadFileModalControl.visible} onCancel={uploadFileModalControl.closeModal}>
        <UploadFileModal
          modalControl={uploadFileModalControl}
          uploadAction={$uploadEntityDocument.effect}
          uploadFileEffector={$uploadEntityDocument}
          type="pdf"
        />
      </ModalCustom>
      <ModalCustom
        open={generateTemplateModalControl.visible}
        onCancel={generateTemplateModalControl.closeModal}
      >
        <GenerateTemplateModal
          modalControl={generateTemplateModalControl}
          kkmTemplateModalControl={kkmTemplateModalControl}
        />
      </ModalCustom>
      <ModalCustom
        open={kkmTemplateModalControl.visible}
        onCancel={kkmTemplateModalControl.closeModal}
        width="80%"
      >
        <KkmTemplateModal modalControl={kkmTemplateModalControl} />
      </ModalCustom>

      <ModalCustom
        open={registerCustomerDeviceModalControl.visible}
        onCancel={registerCustomerDeviceModalControl.closeModal}
        width={700}
      >
        <RegisterCustomerDeviceModal modalControl={registerCustomerDeviceModalControl} afterClose={getKkms} />
      </ModalCustom>

      <ModalCustom
        open={createDeviceModelCombinationsModalControl.visible}
        onCancel={createDeviceModelCombinationsModalControl.closeModal}
        width={700}
      >
        <CreateDeviceModelCombinationsModal
          modalControl={createDeviceModelCombinationsModalControl}
          afterClose={getKkms}
        />
      </ModalCustom>
    </>
  );
};
